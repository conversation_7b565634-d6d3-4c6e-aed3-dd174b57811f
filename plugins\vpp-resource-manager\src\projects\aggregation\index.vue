<template>
  <div class="aggregation-page">
    <!-- 搜索筛选区域 -->
    <div class="search-filter-section">
      <div class="search-controls">
        <!-- 搜索框 -->
        <div class="search-box">
          <el-input
            v-model="searchKeyword"
            placeholder="请输入关键字"
            prefix-icon="el-icon-search"
            class="search-input"
            clearable
            @keyup.enter.native="handleSearch"
            @clear="handleSearch"
          />
        </div>

        <!-- 机组类型下拉选择 -->
        <div class="filter-select">
          <div class="filter-label">机组类型</div>
          <div class="filter-value">{{ selectedType || '全部' }}</div>
          <el-select
            v-model="selectedType"
            placeholder="全部"
            class="type-select"
            @change="handleTypeChange"
          >
            <el-option label="全部" value=""></el-option>
            <el-option label="调峰机组" value="调峰机组"></el-option>
            <el-option label="基荷机组" value="基荷机组"></el-option>
            <el-option label="调频机组" value="调频机组"></el-option>
          </el-select>
          <i class="el-icon-arrow-down filter-arrow"></i>
        </div>
      </div>

      <!-- 新增按钮 -->
      <el-button
        type="primary"
        @click="handleAdd"
        class="add-button"
      >
        新增
      </el-button>
    </div>

    <!-- 数据表格 -->
    <div class="table-section">
      <el-table
        :data="currentPageData"
        style="width: 100%"
        :height="tableHeight"
        class="data-table"
        border
        stripe
        v-loading="loading"
        element-loading-text="加载中..."
      >
        <!-- 序号列 -->
        <el-table-column
          type="index"
          label="序号"
          width="80"
          align="center"
          :index="getIndex"
        />

        <!-- 机组名称列 -->
        <el-table-column
          prop="unitName"
          label="机组名称"
          min-width="200"
          show-overflow-tooltip
        />

        <!-- 机组类型列 -->
        <el-table-column
          prop="unitType"
          label="机组类型"
          width="120"
        />

        <!-- 聚合资源数量列 -->
        <el-table-column
          prop="resourceCount"
          label="聚合资源数量"
          width="140"
        />

        <!-- 装机容量列 -->
        <el-table-column
          prop="installedCapacity"
          label="装机容量(MW)"
          width="140"
        />

        <!-- 可调节容量列 -->
        <el-table-column
          prop="adjustableCapacity"
          label="可调节容量(MW)"
          width="140"
        />

        <!-- 状态列 -->
        <el-table-column
          prop="status"
          label="状态"
          width="100"
        />

        <!-- 操作列 -->
        <el-table-column
          label="操作"
          width="160"
          fixed="right"
        >
          <template slot-scope="scope">
            <el-button
              type="text"
              size="small"
              @click="handleEdit(scope.row)"
              class="edit-btn"
            >
              编辑
            </el-button>
            <el-button
              type="text"
              size="small"
              @click="handleDelete(scope.row)"
              class="delete-btn"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页器 -->
      <div class="pagination-wrapper">
        <div class="pagination-info">
          共 {{ total }} 条记录，第 {{ currentPage }} / {{ Math.ceil(total / pageSize) }} 页
        </div>
        <el-pagination
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-size="pageSize"
          layout="prev, pager, next"
          :total="total"
          class="pagination"
          :pager-count="5"
          small
        />
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "AggregationList",
  data() {
    return {
      // 搜索筛选相关
      searchKeyword: "",
      selectedType: "",

      // 表格相关
      tableData: [],
      loading: false,
      tableHeight: 600,

      // 分页相关
      currentPage: 1,
      pageSize: 15
    };
  },
  computed: {
    // 过滤后的数据
    filteredData() {
      let data = [...this.tableData];

      // 关键字搜索
      if (this.searchKeyword) {
        data = data.filter(item =>
          item.unitName.toLowerCase().includes(this.searchKeyword.toLowerCase())
        );
      }

      // 类型筛选
      if (this.selectedType) {
        data = data.filter(item => item.unitType === this.selectedType);
      }

      return data;
    },

    // 总数
    total() {
      return this.filteredData.length;
    },

    // 当前页数据
    currentPageData() {
      const start = (this.currentPage - 1) * this.pageSize;
      const end = start + this.pageSize;
      return this.filteredData.slice(start, end);
    }
  },
  mounted() {
    this.initData();
    this.calculateTableHeight();
    window.addEventListener('resize', this.calculateTableHeight);
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.calculateTableHeight);
  },
  methods: {
    // 初始化数据
    initData() {
      this.loading = true;
      // 模拟数据 - 根据Figma设计生成
      setTimeout(() => {
        this.tableData = Array.from({ length: 15 }, (_, index) => ({
          id: index + 1,
          unitName: '1#调峰机组',
          unitType: '调峰机组',
          resourceCount: Math.floor(Math.random() * 50) + 20,
          installedCapacity: (Math.random() * 200 + 100).toFixed(1),
          adjustableCapacity: (Math.random() * 100 + 50).toFixed(1),
          status: index % 3 === 0 ? '正常' : index % 3 === 1 ? '离线' : '故障'
        }));
        this.loading = false;
      }, 500);
    },

    // 计算表格高度
    calculateTableHeight() {
      const windowHeight = window.innerHeight;
      const headerHeight = 80; // 顶部导航高度
      const searchHeight = 80; // 搜索区域高度
      const paginationHeight = 60; // 分页器高度
      const padding = 40; // 内边距

      this.tableHeight = windowHeight - headerHeight - searchHeight - paginationHeight - padding;
    },

    // 获取序号
    getIndex(index) {
      return (this.currentPage - 1) * this.pageSize + index + 1;
    },

    // 搜索处理
    handleSearch() {
      this.currentPage = 1;
    },

    // 类型变更处理
    handleTypeChange() {
      this.currentPage = 1;
    },

    // 新增处理
    handleAdd() {
      this.$message.success('新增功能待开发');
    },

    // 编辑处理
    handleEdit(row) {
      this.$message.info(`编辑机组: ${row.unitName}`);
    },

    // 删除处理
    handleDelete(row) {
      this.$confirm(`确定要删除机组 "${row.unitName}" 吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$message.success('删除成功');
        // 这里应该调用删除API
      }).catch(() => {
        this.$message.info('已取消删除');
      });
    },

    // 当前页变更
    handleCurrentChange(val) {
      this.currentPage = val;
    }
  }
};
</script>

<style lang="scss" scoped>
.aggregation-page {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: var(--BG);
  padding: 24px;
  gap: 16px;
}

.search-filter-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: var(--BG1);
  padding: 24px;
  border-radius: 4px;

  .search-controls {
    display: flex;
    gap: 16px;
    align-items: center;
  }

  .search-box {
    position: relative;

    .search-input {
      width: 240px;

      ::v-deep .el-input__inner {
        height: 32px;
        line-height: 32px;
        border-radius: 4px;
        border: 1px solid #C9CDD4;
        background-color: #FFFFFF;
        color: #13171F;
        font-size: 14px;
        padding-left: 32px;

        &::placeholder {
          color: #C9CDD4;
          font-size: 14px;
        }

        &:focus {
          border-color: #C9CDD4;
          box-shadow: 0px 0px 0px 0px rgba(240, 240, 240, 1);
        }
      }

      ::v-deep .el-input__prefix {
        left: 8px;
        color: #C9CDD4;
      }
    }
  }

  .filter-select {
    position: relative;
    width: 240px;
    height: 32px;
    border: 1px solid #C9CDD4;
    border-radius: 4px;
    background-color: #FFFFFF;
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 0 16px;
    cursor: pointer;

    .filter-label {
      font-size: 12px;
      color: #00B45E;
      line-height: 1.5;
      margin-bottom: -2px;
    }

    .filter-value {
      font-size: 14px;
      color: #13171F;
      line-height: 1.4285714285714286;
    }

    .type-select {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      opacity: 0;

      ::v-deep .el-input__inner {
        border: none;
        background: transparent;
      }
    }

    .filter-arrow {
      position: absolute;
      right: 16px;
      top: 50%;
      transform: translateY(-50%);
      color: #424E5F;
      font-size: 12px;
    }
  }

  .add-button {
    height: 32px;
    background-color: #00B45E;
    border-color: #00B45E;
    border-radius: 3px;
    padding: 5px 16px;
    font-size: 14px;
    color: #FFFFFF;

    &:hover,
    &:focus {
      background-color: #00B45E;
      border-color: #00B45E;
      opacity: 0.9;
    }
  }
}

.table-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: #FFFFFF;
  border-radius: 4px;
  overflow: hidden;

  .data-table {
    flex: 1;

    ::v-deep .el-table {
      border: none;

      &::before {
        display: none;
      }
    }

    ::v-deep .el-table__header-wrapper {
      .el-table__header {
        th {
          background-color: #FAFCFF;
          color: #424E5F;
          font-weight: 400;
          font-size: 12px;
          border-bottom: 1px solid #DCDFE6;
          border-right: none;

          &:last-child {
            border-right: none;
          }

          .cell {
            padding: 10px 24px;
            text-align: left;
          }
        }
      }
    }

    ::v-deep .el-table__body-wrapper {
      .el-table__body {
        tr {
          &:hover {
            background-color: #F5F7FA;
          }

          td {
            border-bottom: 1px solid #DCDFE6;
            border-right: none;
            color: #424E5F;
            font-size: 12px;

            &:last-child {
              border-right: none;
            }

            .cell {
              padding: 10px 24px;
              text-align: left;
            }
          }
        }
      }
    }

    // 序号列样式
    ::v-deep .el-table__body {
      .el-table__row {
        .el-table__cell:first-child {
          .cell {
            text-align: center;
            font-size: 12px;
            color: #424E5F;
          }
        }
      }
    }

    ::v-deep .el-table__header {
      .el-table__cell:first-child {
        .cell {
          text-align: center;
        }
      }
    }

    .edit-btn {
      color: #424E5F;
      font-size: 14px;
      padding: 0;
      margin-right: 24px;

      &:hover {
        color: #00B45E;
      }
    }

    .delete-btn {
      color: #F53F3F;
      font-size: 14px;
      padding: 0;

      &:hover {
        color: #F53F3F;
        opacity: 0.8;
      }
    }
  }

  .pagination-wrapper {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 24px;
    border-top: 1px solid #DCDFE6;
    background-color: #FFFFFF;

    .pagination-info {
      font-size: 14px;
      color: #424E5F;
    }

    .pagination {
      ::v-deep .el-pagination {
        .el-pager li {
          background-color: transparent;
          color: #424E5F;
          border: 1px solid #DCDFE6;
          margin: 0 2px;
          min-width: 28px;
          height: 28px;
          line-height: 26px;

          &.active {
            background-color: #00B45E;
            color: #FFFFFF;
            border-color: #00B45E;
          }

          &:hover {
            color: #00B45E;
            border-color: #00B45E;
          }
        }

        .btn-prev,
        .btn-next {
          background-color: transparent;
          color: #424E5F;
          border: 1px solid #DCDFE6;
          min-width: 28px;
          height: 28px;
          line-height: 26px;

          &:hover {
            color: #00B45E;
            border-color: #00B45E;
          }

          &:disabled {
            color: #C0C4CC;
            border-color: #E4E7ED;
          }
        }
      }
    }
  }
}

// 响应式适配
@media (max-width: 768px) {
  .search-filter-section {
    flex-direction: column;
    gap: var(--J2);

    .search-controls {
      flex-direction: column;
      width: 100%;

      .search-input-wrapper,
      .type-select-wrapper {
        width: 100%;

        .search-input,
        .type-select {
          width: 100%;
        }
      }
    }

    .add-button {
      align-self: flex-start;
    }
  }

  .table-section {
    .data-table {
      ::v-deep .el-table__body-wrapper {
        overflow-x: auto;
      }
    }

    .pagination-wrapper {
      justify-content: center;

      .pagination {
        ::v-deep .el-pagination {
          .el-pagination__sizes,
          .el-pagination__jump {
            display: none;
          }
        }
      }
    }
  }
}
</style>